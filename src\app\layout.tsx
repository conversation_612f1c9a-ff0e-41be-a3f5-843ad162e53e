

import "./globals.css";
import "@/i18n";
import AnnouncementBar from "@/components/AnnouncementBar";
import TopBar from "@/components/TopBar";
import Navbar from "@/components/Navbar";
import { UserProvider } from "../context/UserContext";

export const metadata = {
  title: "Your Site Title",
  description: "Your site description",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <UserProvider>
          <AnnouncementBar />
          <TopBar />
          <Navbar />
          {children}
        </UserProvider>
      </body>
    </html>
  );
}
