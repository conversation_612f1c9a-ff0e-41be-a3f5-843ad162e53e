"use client";

import { FaShoppingCart } from "react-icons/fa";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import i18n from "@/i18n";
import { useTranslation } from "react-i18next";
import { useUser } from "@/context/UserContext";

const countries = [
  { name: "Egypt", code: "eg", lang: "ar" },
  { name: "Saudi Arabia", code: "sa", lang: "ar" },
  { name: "UAE", code: "ae", lang: "ar" },
  { name: "Kuwait", code: "kw", lang: "ar" },
  { name: "Qatar", code: "qa", lang: "ar" },
  { name: "Morocco", code: "ma", lang: "ar" },
  { name: "Algeria", code: "dz", lang: "ar" },
  { name: "Jordan", code: "jo", lang: "ar" },
  { name: "Iraq", code: "iq", lang: "ar" },
  { name: "Oman", code: "om", lang: "ar" },
  { name: "Tunisia", code: "tn", lang: "ar" },
  { name: "USA", code: "us", lang: "en" },
  { name: "UK", code: "gb", lang: "en" },
  { name: "Germany", code: "de", lang: "en" },
  { name: "France", code: "fr", lang: "en" },
  { name: "Canada", code: "ca", lang: "en" },
  { name: "Australia", code: "au", lang: "en" },
  { name: "India", code: "in", lang: "en" },
  { name: "China", code: "cn", lang: "en" },
  { name: "Turkey", code: "tr", lang: "en" },
];

export default function TopBar() {
  const [selectedCountry, setSelectedCountry] = useState("Egypt");
  const { t } = useTranslation();
  const [isClient, setIsClient] = useState(false);

  const { user, setUser } = useUser();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    setIsClient(true);
    const countryObj = countries.find((c) => c.name === selectedCountry);
    if (countryObj) {
      i18n.changeLanguage(countryObj.lang);
    }
  }, [selectedCountry]);

  // إغلاق القائمة عند الضغط خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {

      if (
        dropdownRef.current &&
            !dropdownRef.current.contains(event.target)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleLogout = () => {
    setUser(null);
    setDropdownOpen(false);
  };

  if (!isClient) return null;

  return (
    <div className="w-full bg-white text-gray-800 py-3 px-6 shadow-sm">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center space-x-2">
          <Image
            src="/logo.png"
            alt="Logo"
            width={50}
            height={40}
            className="rounded-full"
          />
        </div>

        {/* Search */}
        <div className="flex-1 mx-6">
          <input
            type="text"
            placeholder={t("search_placeholder")}
            className="w-full border border-blue-500 rounded-full px-4 py-2 text-sm text-black focus:outline-none focus:ring-2 focus:ring-blue-400"
          />
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-4 relative">
          {user ? (
            <>
              <button
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className="focus:outline-none"
              >
                <Image
                  src={user.photoURL || "/logo.png"}
                  alt="User"
                  width={36}
                  height={36}
                  className="rounded-full border border-white"
                />
              </button>

              {dropdownOpen && (
                <div
                  ref={dropdownRef}
                  className="absolute right-14 top-12 w-40 bg-white border border-gray-300 rounded shadow-lg z-50"
                >
                  <button
                    onClick={handleLogout}
                    className="block w-full text-right px-4 py-2 text-sm text-blue-600 hover:bg-gray-100"
                  >
                    تسجيل الخروج
                  </button>
                </div>
              )}
            </>
          ) : (
            <Link
              href="/login"
              className="text-sm bg-blue-500 hover:bg-blue-600 px-3 py-1 rounded text-white"
            >
              تسجيل الدخول
            </Link>
          )}

          <FaShoppingCart className="text-xl cursor-pointer hover:text-yellow-600 transition" />

          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none"
          >
            {countries.map((c) => (
              <option key={c.name} value={c.name}>
                {c.name}
              </option>
            ))}
          </select>

          <Image
            src={`https://flagcdn.com/w40/${
              countries.find((c) => c.name === selectedCountry)?.code
            }.png`}
            alt="flag"
            width={30}
            height={20}
            className="rounded"
          />
        </div>
      </div>
    </div>
  );
}
